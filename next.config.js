/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: { unoptimized: true },

  experimental: {
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-accordion',
      '@radix-ui/react-alert-dialog',
      '@radix-ui/react-avatar',
      '@radix-ui/react-checkbox',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-popover',
      '@radix-ui/react-select',
      '@radix-ui/react-tabs',
      '@radix-ui/react-toast',
      'date-fns'
    ]
  },
  webpack: (config, { isServer, dev }) => {
    // Optimize bundle splitting
    if (!isServer) {
      config.optimization.splitChunks = {
        ...config.optimization.splitChunks,
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000,
        cacheGroups: {
          ...config.optimization.splitChunks.cacheGroups,

          // Framework chunk (React, Next.js core)
          framework: {
            name: 'framework',
            test: /[\\/]node_modules[\\/](react|react-dom|next)[\\/]/,
            chunks: 'all',
            priority: 40,
            enforce: true,
          },

          // Charts and visualization libraries
          charts: {
            name: 'charts',
            test: /[\\/]node_modules[\\/](recharts|d3-.*|victory-.*)[\\/]/,
            chunks: 'all',
            priority: 35,
            minChunks: 1,
          },

          // UI component libraries
          ui: {
            name: 'ui',
            test: /[\\/]node_modules[\\/]@radix-ui[\\/]/,
            chunks: 'all',
            priority: 30,
            minChunks: 1,
          },

          // Form handling libraries
          forms: {
            name: 'forms',
            test: /[\\/]node_modules[\\/](react-hook-form|@hookform|zod)[\\/]/,
            chunks: 'all',
            priority: 25,
            minChunks: 1,
          },

          // State management
          state: {
            name: 'state',
            test: /[\\/]node_modules[\\/](@reduxjs|react-redux|redux)[\\/]/,
            chunks: 'all',
            priority: 25,
            minChunks: 1,
          },

          // Utility libraries
          utils: {
            name: 'utils',
            test: /[\\/]node_modules[\\/](date-fns|clsx|class-variance-authority|tailwind-merge)[\\/]/,
            chunks: 'all',
            priority: 20,
            minChunks: 1,
          },

          // Icon libraries
          icons: {
            name: 'icons',
            test: /[\\/]node_modules[\\/]lucide-react[\\/]/,
            chunks: 'all',
            priority: 20,
            minChunks: 1,
          },

          // Common vendor libraries
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            chunks: 'all',
            priority: 10,
            minChunks: 2,
            maxInitialRequests: 25,
            maxAsyncRequests: 25,
          },

          // Common application code
          common: {
            name: 'common',
            chunks: 'all',
            priority: 5,
            minChunks: 2,
            maxInitialRequests: 25,
            maxAsyncRequests: 25,
          },
        },
      };

      // Optimize module concatenation
      if (!dev) {
        config.optimization.concatenateModules = true;
        config.optimization.usedExports = true;
        config.optimization.sideEffects = false;
      }

      // Add bundle analyzer plugin when ANALYZE=true
      if (process.env.ANALYZE === 'true') {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: true,
            reportFilename: '../bundle-analyzer-report.html',
          })
        );
      }
    }
    return config;
  },
};

module.exports = nextConfig;
