'use client';

import React, { useEffect, useRef, useState, ComponentType } from 'react';

interface IntersectionLoaderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  rootMargin?: string;
  threshold?: number;
  triggerOnce?: boolean;
  onIntersect?: () => void;
  className?: string;
}

/**
 * Component that loads its children only when they come into viewport
 * Perfect for lazy loading heavy components below the fold
 */
export default function IntersectionLoader({
  children,
  fallback = null,
  rootMargin = '50px',
  threshold = 0.1,
  triggerOnce = true,
  onIntersect,
  className = '',
}: IntersectionLoaderProps) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        
        if (entry.isIntersecting) {
          setIsIntersecting(true);
          onIntersect?.();
          
          if (triggerOnce) {
            setHasTriggered(true);
            observer.unobserve(element);
          }
        } else if (!triggerOnce) {
          setIsIntersecting(false);
        }
      },
      {
        rootMargin,
        threshold,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [rootMargin, threshold, triggerOnce, onIntersect]);

  const shouldRender = isIntersecting || hasTriggered;

  return (
    <div ref={elementRef} className={className}>
      {shouldRender ? children : fallback}
    </div>
  );
}

/**
 * Hook for intersection observer functionality
 */
export function useIntersectionObserver(
  options: {
    rootMargin?: string;
    threshold?: number;
    triggerOnce?: boolean;
  } = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  const { rootMargin = '50px', threshold = 0.1, triggerOnce = true } = options;

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        
        if (entry.isIntersecting) {
          setIsIntersecting(true);
          
          if (triggerOnce) {
            setHasTriggered(true);
            observer.unobserve(element);
          }
        } else if (!triggerOnce) {
          setIsIntersecting(false);
        }
      },
      { rootMargin, threshold }
    );

    observer.observe(element);

    return () => observer.unobserve(element);
  }, [rootMargin, threshold, triggerOnce]);

  return {
    elementRef,
    isIntersecting: isIntersecting || hasTriggered,
    hasTriggered,
  };
}

/**
 * Higher-order component for intersection-based lazy loading
 */
export function withIntersectionLoader<P extends object>(
  Component: ComponentType<P>,
  loaderOptions: {
    fallback?: React.ReactNode;
    rootMargin?: string;
    threshold?: number;
    triggerOnce?: boolean;
  } = {}
) {
  return function IntersectionLoadedComponent(props: P) {
    const { isIntersecting, elementRef } = useIntersectionObserver(loaderOptions);

    return (
      <div ref={elementRef as React.RefObject<HTMLDivElement>}>
        {isIntersecting ? (
          <Component {...props} />
        ) : (
          loaderOptions.fallback || (
            <div className="h-64 bg-netcare-white/5 rounded-lg animate-pulse flex items-center justify-center">
              <div className="text-netcare-white/50">Loading...</div>
            </div>
          )
        )}
      </div>
    );
  };
}

/**
 * Skeleton component for intersection loading
 */
export function IntersectionSkeleton({ 
  height = 'h-64', 
  className = '' 
}: { 
  height?: string; 
  className?: string; 
}) {
  return (
    <div className={`${height} bg-netcare-white/5 rounded-lg animate-pulse flex items-center justify-center ${className}`}>
      <div className="text-netcare-white/50 text-sm">Loading content...</div>
    </div>
  );
}
