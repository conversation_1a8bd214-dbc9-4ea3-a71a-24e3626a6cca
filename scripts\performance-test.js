#!/usr/bin/env node

/**
 * Performance testing script for Netcare Claims Portal
 * Tests bundle sizes, loading times, and optimization effectiveness
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  buildDir: '.next',
  outputFile: 'performance-report.json',
  thresholds: {
    maxBundleSize: 250000, // 250KB
    maxChunkSize: 100000,  // 100KB
    maxLoadTime: 3000,     // 3 seconds
    minCacheHitRate: 80,   // 80%
  },
  routes: [
    '/',
    '/dashboard',
    '/new-claim',
    '/upload-documents',
    '/support',
  ],
};

class PerformanceTester {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      bundleAnalysis: {},
      routeAnalysis: {},
      optimizationMetrics: {},
      recommendations: [],
    };
  }

  // Run complete performance test suite
  async runTests() {
    console.log('🚀 Starting performance tests...\n');

    try {
      // 1. Build the application
      await this.buildApplication();

      // 2. Analyze bundle sizes
      await this.analyzeBundles();

      // 3. Test route performance
      await this.testRoutes();

      // 4. Generate recommendations
      this.generateRecommendations();

      // 5. Save results
      this.saveResults();

      // 6. Display summary
      this.displaySummary();

    } catch (error) {
      console.error('❌ Performance tests failed:', error);
      process.exit(1);
    }
  }

  // Build the application
  async buildApplication() {
    console.log('📦 Building application...');
    try {
      execSync('npm run build', { stdio: 'inherit' });
      console.log('✅ Build completed\n');
    } catch (error) {
      throw new Error('Build failed');
    }
  }

  // Analyze bundle sizes and chunks
  async analyzeBundles() {
    console.log('📊 Analyzing bundles...');

    const buildManifest = this.readBuildManifest();
    const chunks = this.getChunkSizes();

    this.results.bundleAnalysis = {
      totalSize: chunks.reduce((sum, chunk) => sum + chunk.size, 0),
      chunkCount: chunks.length,
      chunks: chunks.sort((a, b) => b.size - a.size),
      largestChunk: chunks.reduce((max, chunk) => chunk.size > max.size ? chunk : max, chunks[0]),
      averageChunkSize: chunks.reduce((sum, chunk) => sum + chunk.size, 0) / chunks.length,
    };

    // Check thresholds
    const violations = [];
    if (this.results.bundleAnalysis.totalSize > CONFIG.thresholds.maxBundleSize) {
      violations.push(`Total bundle size exceeds threshold: ${this.formatSize(this.results.bundleAnalysis.totalSize)}`);
    }

    chunks.forEach(chunk => {
      if (chunk.size > CONFIG.thresholds.maxChunkSize) {
        violations.push(`Chunk ${chunk.name} exceeds size threshold: ${this.formatSize(chunk.size)}`);
      }
    });

    this.results.bundleAnalysis.violations = violations;
    console.log(`✅ Bundle analysis completed (${chunks.length} chunks)\n`);
  }

  // Test individual route performance
  async testRoutes() {
    console.log('🔍 Testing route performance...');

    for (const route of CONFIG.routes) {
      console.log(`  Testing ${route}...`);
      
      const routeMetrics = await this.testRoute(route);
      this.results.routeAnalysis[route] = routeMetrics;
    }

    console.log('✅ Route performance testing completed\n');
  }

  // Test individual route
  async testRoute(route) {
    // This would typically use a headless browser like Puppeteer
    // For now, we'll simulate the metrics based on bundle analysis
    
    const routeChunks = this.getRouteChunks(route);
    const totalSize = routeChunks.reduce((sum, chunk) => sum + chunk.size, 0);
    
    // Simulate load time based on chunk sizes (rough estimation)
    const estimatedLoadTime = Math.max(500, totalSize / 1000); // 1KB per ms baseline
    
    return {
      chunkCount: routeChunks.length,
      totalSize,
      estimatedLoadTime,
      chunks: routeChunks,
      cacheHitRate: Math.random() * 20 + 80, // Simulate 80-100% cache hit rate
    };
  }

  // Generate optimization recommendations
  generateRecommendations() {
    console.log('💡 Generating recommendations...');

    const recommendations = [];

    // Bundle size recommendations
    if (this.results.bundleAnalysis.totalSize > CONFIG.thresholds.maxBundleSize) {
      recommendations.push({
        type: 'bundle-size',
        priority: 'high',
        message: 'Total bundle size is too large. Consider more aggressive code splitting.',
        action: 'Implement route-based and component-based code splitting',
      });
    }

    // Large chunk recommendations
    const largeChunks = this.results.bundleAnalysis.chunks.filter(
      chunk => chunk.size > CONFIG.thresholds.maxChunkSize
    );

    if (largeChunks.length > 0) {
      recommendations.push({
        type: 'chunk-size',
        priority: 'medium',
        message: `${largeChunks.length} chunks exceed size threshold`,
        action: 'Split large chunks into smaller, more focused bundles',
        chunks: largeChunks.map(chunk => chunk.name),
      });
    }

    // Route performance recommendations
    Object.entries(this.results.routeAnalysis).forEach(([route, metrics]) => {
      if (metrics.estimatedLoadTime > CONFIG.thresholds.maxLoadTime) {
        recommendations.push({
          type: 'route-performance',
          priority: 'medium',
          message: `Route ${route} has slow estimated load time`,
          action: 'Implement lazy loading and preloading for this route',
          route,
          loadTime: metrics.estimatedLoadTime,
        });
      }
    });

    this.results.recommendations = recommendations;
    console.log(`✅ Generated ${recommendations.length} recommendations\n`);
  }

  // Read Next.js build manifest
  readBuildManifest() {
    try {
      const manifestPath = path.join(CONFIG.buildDir, 'build-manifest.json');
      return JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    } catch (error) {
      console.warn('⚠️ Could not read build manifest');
      return {};
    }
  }

  // Get chunk sizes from build directory
  getChunkSizes() {
    const chunks = [];
    const staticDir = path.join(CONFIG.buildDir, 'static', 'chunks');

    if (!fs.existsSync(staticDir)) {
      console.warn('⚠️ Static chunks directory not found');
      return chunks;
    }

    const files = fs.readdirSync(staticDir);
    
    files.forEach(file => {
      if (file.endsWith('.js')) {
        const filePath = path.join(staticDir, file);
        const stats = fs.statSync(filePath);
        
        chunks.push({
          name: file,
          size: stats.size,
          path: filePath,
        });
      }
    });

    return chunks;
  }

  // Get chunks for a specific route
  getRouteChunks(route) {
    // This is a simplified implementation
    // In a real scenario, you'd analyze the build manifest
    const allChunks = this.results.bundleAnalysis.chunks || [];
    
    // Return a subset of chunks that would be loaded for this route
    return allChunks.filter(chunk => {
      // Framework chunks are always loaded
      if (chunk.name.includes('framework') || chunk.name.includes('main')) {
        return true;
      }
      
      // Route-specific chunks
      if (route === '/dashboard' && chunk.name.includes('dashboard')) {
        return true;
      }
      
      // Add more route-specific logic here
      return Math.random() > 0.7; // Randomly include some chunks for simulation
    });
  }

  // Format file size
  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Save results to file
  saveResults() {
    fs.writeFileSync(CONFIG.outputFile, JSON.stringify(this.results, null, 2));
    console.log(`💾 Results saved to ${CONFIG.outputFile}`);
  }

  // Display summary
  displaySummary() {
    console.log('\n📋 PERFORMANCE SUMMARY');
    console.log('========================\n');

    // Bundle summary
    console.log('📦 Bundle Analysis:');
    console.log(`   Total Size: ${this.formatSize(this.results.bundleAnalysis.totalSize)}`);
    console.log(`   Chunks: ${this.results.bundleAnalysis.chunkCount}`);
    console.log(`   Largest Chunk: ${this.results.bundleAnalysis.largestChunk?.name} (${this.formatSize(this.results.bundleAnalysis.largestChunk?.size || 0)})`);
    console.log(`   Average Chunk Size: ${this.formatSize(this.results.bundleAnalysis.averageChunkSize)}\n`);

    // Route summary
    console.log('🔍 Route Performance:');
    Object.entries(this.results.routeAnalysis).forEach(([route, metrics]) => {
      console.log(`   ${route}: ${this.formatSize(metrics.totalSize)} (${metrics.chunkCount} chunks)`);
    });
    console.log('');

    // Recommendations
    if (this.results.recommendations.length > 0) {
      console.log('💡 Recommendations:');
      this.results.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. [${rec.priority.toUpperCase()}] ${rec.message}`);
        console.log(`      Action: ${rec.action}`);
      });
    } else {
      console.log('✅ No performance issues found!');
    }

    console.log('\n🎉 Performance testing completed!');
  }
}

// Run the tests
if (require.main === module) {
  const tester = new PerformanceTester();
  tester.runTests().catch(console.error);
}

module.exports = PerformanceTester;
