// Service Worker for Advanced Caching and Code Splitting Optimization
const CACHE_NAME = 'netcare-claims-v1';
const STATIC_CACHE = 'netcare-static-v1';
const DYNAMIC_CACHE = 'netcare-dynamic-v1';

// Critical resources to cache immediately
const CRITICAL_RESOURCES = [
  '/',
  '/dashboard',
  '/manifest.json',
  // Add critical CSS and JS files here
];

// Chunk patterns for intelligent caching
const CHUNK_PATTERNS = {
  framework: /\/_next\/static\/chunks\/(framework|main|webpack)/,
  vendor: /\/_next\/static\/chunks\/vendor/,
  pages: /\/_next\/static\/chunks\/pages/,
  app: /\/_next\/static\/chunks\/app/,
  lazy: /\/_next\/static\/chunks\/.*\.js$/,
};

// Cache strategies
const CACHE_STRATEGIES = {
  // Critical resources: Cache first, network fallback
  critical: 'cache-first',
  // Static assets: Stale while revalidate
  static: 'stale-while-revalidate',
  // API calls: Network first, cache fallback
  api: 'network-first',
  // Lazy chunks: Cache first with background update
  chunks: 'cache-first-bg-update',
};

// Install event - cache critical resources
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('📦 Caching critical resources');
        return cache.addAll(CRITICAL_RESOURCES);
      })
      .then(() => {
        console.log('✅ Critical resources cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('❌ Failed to cache critical resources:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('🗑️ Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ Service Worker activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') return;

  // Skip cross-origin requests (unless specific domains)
  if (url.origin !== location.origin) return;

  // Determine cache strategy based on request
  const strategy = getCacheStrategy(request);
  
  event.respondWith(
    handleRequest(request, strategy)
  );
});

// Determine appropriate cache strategy
function getCacheStrategy(request) {
  const url = request.url;
  
  // Framework and vendor chunks - cache first
  if (CHUNK_PATTERNS.framework.test(url) || CHUNK_PATTERNS.vendor.test(url)) {
    return CACHE_STRATEGIES.critical;
  }
  
  // Lazy-loaded chunks - cache first with background update
  if (CHUNK_PATTERNS.lazy.test(url)) {
    return CACHE_STRATEGIES.chunks;
  }
  
  // Static assets
  if (url.includes('/_next/static/') || url.includes('/static/')) {
    return CACHE_STRATEGIES.static;
  }
  
  // API calls
  if (url.includes('/api/')) {
    return CACHE_STRATEGIES.api;
  }
  
  // Default to stale while revalidate
  return CACHE_STRATEGIES.static;
}

// Handle request based on strategy
async function handleRequest(request, strategy) {
  const cacheName = getCacheName(request);
  
  switch (strategy) {
    case 'cache-first':
      return cacheFirst(request, cacheName);
    
    case 'cache-first-bg-update':
      return cacheFirstWithBackgroundUpdate(request, cacheName);
    
    case 'network-first':
      return networkFirst(request, cacheName);
    
    case 'stale-while-revalidate':
      return staleWhileRevalidate(request, cacheName);
    
    default:
      return fetch(request);
  }
}

// Get appropriate cache name
function getCacheName(request) {
  const url = request.url;
  
  if (CRITICAL_RESOURCES.some(resource => url.includes(resource))) {
    return STATIC_CACHE;
  }
  
  if (url.includes('/_next/static/')) {
    return STATIC_CACHE;
  }
  
  return DYNAMIC_CACHE;
}

// Cache first strategy
async function cacheFirst(request, cacheName) {
  try {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      console.log('📦 Serving from cache:', request.url);
      return cachedResponse;
    }
    
    console.log('🌐 Fetching from network:', request.url);
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('❌ Cache first failed:', error);
    return new Response('Offline', { status: 503 });
  }
}

// Cache first with background update
async function cacheFirstWithBackgroundUpdate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Background update
  const networkUpdate = fetch(request)
    .then((response) => {
      if (response.ok) {
        cache.put(request, response.clone());
      }
      return response;
    })
    .catch(() => {
      // Silently fail background updates
    });
  
  if (cachedResponse) {
    console.log('📦 Serving from cache with background update:', request.url);
    return cachedResponse;
  }
  
  console.log('🌐 No cache, waiting for network:', request.url);
  return networkUpdate;
}

// Network first strategy
async function networkFirst(request, cacheName) {
  try {
    console.log('🌐 Network first:', request.url);
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('📦 Network failed, trying cache:', request.url);
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return new Response('Offline', { status: 503 });
  }
}

// Stale while revalidate strategy
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Background revalidation
  const networkUpdate = fetch(request)
    .then((response) => {
      if (response.ok) {
        cache.put(request, response.clone());
      }
      return response;
    })
    .catch(() => {
      // Silently fail revalidation
    });
  
  if (cachedResponse) {
    console.log('📦 Serving stale from cache:', request.url);
    return cachedResponse;
  }
  
  console.log('🌐 No cache, waiting for network:', request.url);
  return networkUpdate;
}

// Message handling for cache management
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
    
    case 'CACHE_URLS':
      cacheUrls(payload.urls);
      break;
    
    case 'CLEAR_CACHE':
      clearCache(payload.cacheName);
      break;
    
    case 'GET_CACHE_INFO':
      getCacheInfo().then((info) => {
        event.ports[0].postMessage(info);
      });
      break;
  }
});

// Cache specific URLs
async function cacheUrls(urls) {
  const cache = await caches.open(DYNAMIC_CACHE);
  return Promise.all(
    urls.map(url => 
      fetch(url)
        .then(response => {
          if (response.ok) {
            return cache.put(url, response);
          }
        })
        .catch(() => {
          // Silently fail individual URL caching
        })
    )
  );
}

// Clear specific cache
async function clearCache(cacheName) {
  return caches.delete(cacheName);
}

// Get cache information
async function getCacheInfo() {
  const cacheNames = await caches.keys();
  const cacheInfo = {};
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    cacheInfo[cacheName] = {
      count: keys.length,
      urls: keys.map(request => request.url),
    };
  }
  
  return cacheInfo;
}
