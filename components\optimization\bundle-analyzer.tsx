'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  Download, 
  Alert<PERSON>riangle, 
  CheckCircle, 
  Clock,
  Zap,
  Package
} from 'lucide-react';

interface BundleMetrics {
  totalSize: number;
  chunkCount: number;
  loadTime: number;
  cacheHitRate: number;
  chunks: ChunkInfo[];
}

interface ChunkInfo {
  name: string;
  size: number;
  loadTime: number;
  cached: boolean;
  priority: 'high' | 'medium' | 'low';
}

interface BundleAnalyzerProps {
  enabled?: boolean;
  showInProduction?: boolean;
  onMetricsUpdate?: (metrics: BundleMetrics) => void;
}

/**
 * Runtime bundle analyzer that monitors chunk loading and performance
 */
export default function BundleAnalyzer({ 
  enabled = true, 
  showInProduction = false,
  onMetricsUpdate 
}: BundleAnalyzerProps) {
  const [metrics, setMetrics] = useState<BundleMetrics | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const shouldShow = enabled && (process.env.NODE_ENV === 'development' || showInProduction);

  const analyzeBundle = useCallback(async () => {
    if (!shouldShow) return;

    setIsAnalyzing(true);
    
    try {
      // Get performance entries for JavaScript resources
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const jsResources = resources.filter(resource => 
        resource.name.includes('.js') && 
        (resource.name.includes('chunks') || resource.name.includes('static'))
      );

      // Calculate metrics
      const totalSize = jsResources.reduce((sum, resource) => sum + (resource.transferSize || 0), 0);
      const totalLoadTime = jsResources.reduce((sum, resource) => sum + resource.duration, 0);
      const cachedResources = jsResources.filter(resource => resource.transferSize === 0);
      const cacheHitRate = jsResources.length > 0 ? (cachedResources.length / jsResources.length) * 100 : 0;

      // Create chunk info
      const chunks: ChunkInfo[] = jsResources.map(resource => {
        const name = resource.name.split('/').pop() || 'unknown';
        const size = resource.transferSize || resource.encodedBodySize || 0;
        const loadTime = resource.duration;
        const cached = resource.transferSize === 0;
        
        // Determine priority based on chunk name
        let priority: 'high' | 'medium' | 'low' = 'medium';
        if (name.includes('main') || name.includes('framework')) priority = 'high';
        if (name.includes('vendor') || name.includes('commons')) priority = 'medium';
        if (name.includes('lazy') || name.includes('chunk')) priority = 'low';

        return { name, size, loadTime, cached, priority };
      });

      const bundleMetrics: BundleMetrics = {
        totalSize,
        chunkCount: jsResources.length,
        loadTime: totalLoadTime,
        cacheHitRate,
        chunks: chunks.sort((a, b) => b.size - a.size), // Sort by size descending
      };

      setMetrics(bundleMetrics);
      onMetricsUpdate?.(bundleMetrics);
    } catch (error) {
      console.error('Bundle analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [shouldShow, onMetricsUpdate]);

  const exportMetrics = useCallback(() => {
    if (!metrics) return;

    const data = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics,
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bundle-analysis-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [metrics]);

  const formatSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return 'bg-red-500/20 text-red-300 border-red-400/40';
      case 'medium': return 'bg-yellow-500/20 text-yellow-300 border-yellow-400/40';
      case 'low': return 'bg-green-500/20 text-green-300 border-green-400/40';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-400/40';
    }
  };

  const getPerformanceStatus = (metrics: BundleMetrics) => {
    const avgChunkSize = metrics.totalSize / metrics.chunkCount;
    const avgLoadTime = metrics.loadTime / metrics.chunkCount;

    if (avgChunkSize > 100000 || avgLoadTime > 1000) { // 100KB or 1s
      return { status: 'warning', icon: AlertTriangle, color: 'text-yellow-400' };
    } else if (metrics.cacheHitRate > 80) {
      return { status: 'excellent', icon: CheckCircle, color: 'text-green-400' };
    } else {
      return { status: 'good', icon: Zap, color: 'text-blue-400' };
    }
  };

  useEffect(() => {
    if (shouldShow) {
      // Analyze bundle after initial load
      const timer = setTimeout(analyzeBundle, 2000);
      return () => clearTimeout(timer);
    }
  }, [shouldShow, analyzeBundle]);

  if (!shouldShow) return null;

  return (
    <>
      {/* Floating toggle button */}
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(!isVisible)}
          className="bg-netcare-slate/90 hover:bg-netcare-slate text-white rounded-full p-3 shadow-lg"
          size="sm"
        >
          <BarChart3 className="w-5 h-5" />
        </Button>
      </div>

      {/* Bundle analyzer panel */}
      {isVisible && (
        <div className="fixed bottom-20 right-4 w-96 max-h-96 overflow-y-auto z-50">
          <Card className="bg-netcare-slate/95 backdrop-blur-sm border-netcare-gold/30 text-white">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Package className="w-5 h-5 text-netcare-cyan" />
                  Bundle Analyzer
                </CardTitle>
                <div className="flex gap-2">
                  <Button
                    onClick={analyzeBundle}
                    disabled={isAnalyzing}
                    size="sm"
                    variant="ghost"
                    className="text-netcare-cyan hover:bg-netcare-cyan/20"
                  >
                    {isAnalyzing ? <Clock className="w-4 h-4 animate-spin" /> : 'Refresh'}
                  </Button>
                  <Button
                    onClick={exportMetrics}
                    disabled={!metrics}
                    size="sm"
                    variant="ghost"
                    className="text-netcare-cyan hover:bg-netcare-cyan/20"
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {metrics ? (
                <>
                  {/* Overall metrics */}
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="bg-netcare-white/10 p-3 rounded-lg">
                      <div className="text-netcare-white/70">Total Size</div>
                      <div className="font-bold text-netcare-cyan">{formatSize(metrics.totalSize)}</div>
                    </div>
                    <div className="bg-netcare-white/10 p-3 rounded-lg">
                      <div className="text-netcare-white/70">Chunks</div>
                      <div className="font-bold text-netcare-cyan">{metrics.chunkCount}</div>
                    </div>
                    <div className="bg-netcare-white/10 p-3 rounded-lg">
                      <div className="text-netcare-white/70">Load Time</div>
                      <div className="font-bold text-netcare-cyan">{metrics.loadTime.toFixed(0)}ms</div>
                    </div>
                    <div className="bg-netcare-white/10 p-3 rounded-lg">
                      <div className="text-netcare-white/70">Cache Hit</div>
                      <div className="font-bold text-netcare-cyan">{metrics.cacheHitRate.toFixed(1)}%</div>
                    </div>
                  </div>

                  {/* Performance status */}
                  <div className="flex items-center gap-2 p-2 bg-netcare-white/5 rounded-lg">
                    {(() => {
                      const { status, icon: Icon, color } = getPerformanceStatus(metrics);
                      return (
                        <>
                          <Icon className={`w-4 h-4 ${color}`} />
                          <span className="text-sm capitalize">{status} Performance</span>
                        </>
                      );
                    })()}
                  </div>

                  {/* Top chunks */}
                  <div>
                    <div className="text-sm font-medium text-netcare-white/90 mb-2">
                      Largest Chunks
                    </div>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {metrics.chunks.slice(0, 5).map((chunk, index) => (
                        <div key={index} className="flex items-center justify-between text-xs p-2 bg-netcare-white/5 rounded">
                          <div className="flex items-center gap-2 flex-1 min-w-0">
                            <span className="truncate font-mono">{chunk.name}</span>
                            <Badge className={getPriorityColor(chunk.priority)}>
                              {chunk.priority}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2 text-netcare-white/70">
                            {chunk.cached && <CheckCircle className="w-3 h-3 text-green-400" />}
                            <span>{formatSize(chunk.size)}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-4 text-netcare-white/70">
                  Click Refresh to analyze bundle
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
}
