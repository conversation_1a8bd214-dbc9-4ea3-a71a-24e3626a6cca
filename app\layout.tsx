import './globals.css';
import type { Metadata } from 'next';
import { Open_Sans } from 'next/font/google';
import { ReduxProvider } from '@/components/providers/redux-provider';
import OptimizationProvider from '@/components/providers/optimization-provider';
import { ClientBodyWrapper } from '@/components/client-body-wrapper';
import MainLayout from '@/components/layout/MainLayout';
import BundleAnalyzer from '@/components/optimization/bundle-analyzer';
import ServiceWorkerManager, { OfflineIndicator, UpdateNotification } from '@/components/optimization/service-worker-manager';
import { PerformanceDebugger } from '@/components/performance/performance-monitor';

const openSans = Open_Sans({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Netcare App',
  description: 'Professional healthcare management system',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {

  return (
    <html lang="en">
      <body className={openSans.className} suppressHydrationWarning={true}>
        <ClientBodyWrapper>
          <ReduxProvider>
            <OptimizationProvider>
              <MainLayout>
                {children}
              </MainLayout>

              {/* Advanced Optimization Components */}
              <ServiceWorkerManager enabled={true} />
              <BundleAnalyzer enabled={true} />
              <PerformanceDebugger />

              {/* User Experience Components */}
              <OfflineIndicator />
              <UpdateNotification />
            </OptimizationProvider>
          </ReduxProvider>
        </ClientBodyWrapper>
      </body>
    </html>
  );
}