'use client';

import React, { useEffect } from 'react';
import IntelligentPreloader, { createPreloadConfig } from '@/components/optimization/intelligent-preloader';
import ResourcePrioritizer, { createResourceConfig } from '@/components/optimization/resource-prioritizer';

export default function OptimizationProvider({ children }: { children: React.ReactNode }) {
  // Preload configurations for intelligent loading
  const preloadConfigs = [
    createPreloadConfig.onHover('/dashboard', () => import('@/components/dashboard/analytics-charts')),
    createPreloadConfig.onIdle('/upload-documents', () => import('@/components/forms/upload-form')),
    createPreloadConfig.onScroll('/new-claim', () => import('@/components/forms/claim-form'), 30),
    createPreloadConfig.afterTime('/support', () => import('@/components/forms/contact-form'), 10000),
  ];

  // Critical resources to prioritize
  const criticalResources = [
    createResourceConfig.font('/fonts/open-sans-regular.woff2'),
    createResourceConfig.font('/fonts/open-sans-bold.woff2'),
    createResourceConfig.critical('/_next/static/chunks/main.js'),
  ];

  return (
    <>
      {children}
      <IntelligentPreloader preloadConfigs={preloadConfigs} />
      <ResourcePrioritizer resources={criticalResources} />
    </>
  );
}
