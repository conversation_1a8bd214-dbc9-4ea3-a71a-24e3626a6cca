'use client';

import { useEffect, useState, useCallback } from 'react';

interface ServiceWorkerManagerProps {
  enabled?: boolean;
  onUpdate?: (registration: ServiceWorkerRegistration) => void;
  onOffline?: () => void;
  onOnline?: () => void;
}

interface CacheInfo {
  [cacheName: string]: {
    count: number;
    urls: string[];
  };
}

/**
 * Service Worker manager for advanced caching and offline functionality
 */
export default function ServiceWorkerManager({
  enabled = true,
  onUpdate,
  onOffline,
  onOnline,
}: ServiceWorkerManagerProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [updateAvailable, setUpdateAvailable] = useState(false);

  // Register service worker
  const registerServiceWorker = useCallback(async () => {
    if (!enabled || typeof window === 'undefined' || !('serviceWorker' in navigator)) {
      return;
    }

    try {
      console.log('🔧 Registering service worker...');
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
      });

      setSwRegistration(registration);

      // Check for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('🔄 Service worker update available');
              setUpdateAvailable(true);
              onUpdate?.(registration);
            }
          });
        }
      });

      console.log('✅ Service worker registered successfully');
    } catch (error) {
      console.error('❌ Service worker registration failed:', error);
    }
  }, [enabled, onUpdate]);

  // Handle online/offline status
  const handleOnline = useCallback(() => {
    setIsOnline(true);
    onOnline?.();
    console.log('🌐 Back online');
  }, [onOnline]);

  const handleOffline = useCallback(() => {
    setIsOnline(false);
    onOffline?.();
    console.log('📴 Gone offline');
  }, [onOffline]);

  // Update service worker
  const updateServiceWorker = useCallback(() => {
    if (swRegistration?.waiting) {
      swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  }, [swRegistration]);

  // Preload critical resources
  const preloadResources = useCallback((urls: string[]) => {
    if (swRegistration?.active) {
      swRegistration.active.postMessage({
        type: 'CACHE_URLS',
        payload: { urls },
      });
    }
  }, [swRegistration]);

  // Get cache information
  const getCacheInfo = useCallback((): Promise<CacheInfo> => {
    return new Promise((resolve) => {
      if (!swRegistration?.active) {
        resolve({});
        return;
      }

      const messageChannel = new MessageChannel();
      messageChannel.port1.onmessage = (event) => {
        resolve(event.data);
      };

      swRegistration.active.postMessage(
        { type: 'GET_CACHE_INFO' },
        [messageChannel.port2]
      );
    });
  }, [swRegistration]);

  // Clear specific cache
  const clearCache = useCallback((cacheName: string) => {
    if (swRegistration?.active) {
      swRegistration.active.postMessage({
        type: 'CLEAR_CACHE',
        payload: { cacheName },
      });
    }
  }, [swRegistration]);

  useEffect(() => {
    registerServiceWorker();

    // Listen for online/offline events
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial online status
    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [registerServiceWorker, handleOnline, handleOffline]);

  // Expose methods for external use
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).swManager = {
        updateServiceWorker,
        preloadResources,
        getCacheInfo,
        clearCache,
        isOnline,
        updateAvailable,
      };
    }
  }, [updateServiceWorker, preloadResources, getCacheInfo, clearCache, isOnline, updateAvailable]);

  return null; // This component doesn't render anything
}

/**
 * Hook for service worker functionality
 */
export function useServiceWorker() {
  const [isOnline, setIsOnline] = useState(true);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    setIsOnline(navigator.onLine);

    // Check for existing service worker
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.getRegistration().then((registration) => {
        setSwRegistration(registration || null);
      });
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const preloadResources = useCallback((urls: string[]) => {
    if (swRegistration?.active) {
      swRegistration.active.postMessage({
        type: 'CACHE_URLS',
        payload: { urls },
      });
    }
  }, [swRegistration]);

  const updateServiceWorker = useCallback(() => {
    if (swRegistration?.waiting) {
      swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  }, [swRegistration]);

  return {
    isOnline,
    updateAvailable,
    preloadResources,
    updateServiceWorker,
    swRegistration,
  };
}

/**
 * Offline indicator component
 */
export function OfflineIndicator() {
  const { isOnline } = useServiceWorker();

  if (isOnline) return null;

  return (
    <div className="fixed top-0 left-0 right-0 bg-red-600 text-white text-center py-2 z-50">
      <div className="flex items-center justify-center gap-2">
        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
        <span className="text-sm font-medium">You're offline. Some features may be limited.</span>
      </div>
    </div>
  );
}

/**
 * Update notification component
 */
export function UpdateNotification() {
  const { updateAvailable, updateServiceWorker } = useServiceWorker();

  if (!updateAvailable) return null;

  return (
    <div className="fixed bottom-4 left-4 bg-netcare-cyan text-white p-4 rounded-lg shadow-lg z-50 max-w-sm">
      <div className="flex items-center justify-between gap-3">
        <div>
          <div className="font-medium">Update Available</div>
          <div className="text-sm opacity-90">A new version of the app is ready.</div>
        </div>
        <button
          onClick={updateServiceWorker}
          className="bg-white text-netcare-cyan px-3 py-1 rounded text-sm font-medium hover:bg-gray-100 transition-colors"
        >
          Update
        </button>
      </div>
    </div>
  );
}
