'use client';

import { useEffect, useCallback, useRef } from 'react';

interface ResourcePriority {
  url: string;
  priority: 'high' | 'low';
  as?: 'script' | 'style' | 'font' | 'image';
  crossOrigin?: 'anonymous' | 'use-credentials';
  type?: string;
}

interface ResourcePrioritizerProps {
  resources: ResourcePriority[];
  enabled?: boolean;
}

/**
 * Component that manages resource loading priorities using Resource Hints
 * and modern browser APIs for optimal performance
 */
export default function ResourcePrioritizer({ 
  resources, 
  enabled = true 
}: ResourcePrioritizerProps) {
  const processedResources = useRef(new Set<string>());

  const addResourceHint = useCallback((resource: ResourcePriority) => {
    if (processedResources.current.has(resource.url)) return;

    const link = document.createElement('link');
    
    // Set the appropriate rel attribute based on priority
    if (resource.priority === 'high') {
      link.rel = 'preload';
      link.as = resource.as || 'script';
    } else {
      link.rel = 'prefetch';
    }

    link.href = resource.url;
    
    if (resource.crossOrigin) {
      link.crossOrigin = resource.crossOrigin;
    }
    
    if (resource.type) {
      link.type = resource.type;
    }

    // Add to document head
    document.head.appendChild(link);
    processedResources.current.add(resource.url);

    console.log(`📦 Added resource hint: ${link.rel} for ${resource.url}`);
  }, []);

  const prioritizeResources = useCallback(() => {
    if (!enabled) return;

    // Sort resources by priority (high first)
    const sortedResources = [...resources].sort((a, b) => {
      const priorityOrder = { high: 1, low: 2 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });

    // Add resource hints with slight delays to avoid overwhelming the browser
    sortedResources.forEach((resource, index) => {
      setTimeout(() => {
        addResourceHint(resource);
      }, index * 100); // 100ms delay between each resource
    });
  }, [resources, enabled, addResourceHint]);

  useEffect(() => {
    // Wait for initial page load before adding resource hints
    if (document.readyState === 'complete') {
      prioritizeResources();
    } else {
      window.addEventListener('load', prioritizeResources);
      return () => window.removeEventListener('load', prioritizeResources);
    }
  }, [prioritizeResources]);

  return null;
}

/**
 * Hook for managing resource priorities
 */
export function useResourcePrioritizer() {
  const processedResources = useRef(new Set<string>());

  const preloadResource = useCallback((url: string, as: string = 'script', priority: 'high' | 'low' = 'high') => {
    if (processedResources.current.has(url)) return;

    const link = document.createElement('link');
    link.rel = priority === 'high' ? 'preload' : 'prefetch';
    link.href = url;
    link.as = as;
    
    document.head.appendChild(link);
    processedResources.current.add(url);
  }, []);

  const preloadFont = useCallback((url: string, type: string = 'font/woff2') => {
    preloadResource(url, 'font', 'high');
    
    // Add type and crossorigin for fonts
    const links = document.querySelectorAll(`link[href="${url}"]`);
    links.forEach(link => {
      link.setAttribute('type', type);
      link.setAttribute('crossorigin', 'anonymous');
    });
  }, [preloadResource]);

  const preloadImage = useCallback((url: string, priority: 'high' | 'low' = 'low') => {
    preloadResource(url, 'image', priority);
  }, [preloadResource]);

  const preloadScript = useCallback((url: string, priority: 'high' | 'low' = 'high') => {
    preloadResource(url, 'script', priority);
  }, [preloadResource]);

  const preloadStyle = useCallback((url: string, priority: 'high' | 'low' = 'high') => {
    preloadResource(url, 'style', priority);
  }, [preloadResource]);

  return {
    preloadResource,
    preloadFont,
    preloadImage,
    preloadScript,
    preloadStyle,
    processedResources: Array.from(processedResources.current),
  };
}

/**
 * Critical resource preloader for above-the-fold content
 */
export function CriticalResourcePreloader({ children }: { children: React.ReactNode }) {
  const { preloadFont, preloadStyle } = useResourcePrioritizer();

  useEffect(() => {
    // Preload critical fonts
    preloadFont('/fonts/open-sans-regular.woff2');
    preloadFont('/fonts/open-sans-bold.woff2');
    
    // Preload critical CSS (if using external stylesheets)
    // preloadStyle('/css/critical.css');
  }, [preloadFont, preloadStyle]);

  return <>{children}</>;
}

/**
 * Adaptive resource loading based on network conditions
 */
export function AdaptiveResourceLoader({ 
  highQualityResources, 
  lowQualityResources,
  children 
}: {
  highQualityResources: ResourcePriority[];
  lowQualityResources: ResourcePriority[];
  children: React.ReactNode;
}) {
  const { preloadResource } = useResourcePrioritizer();

  useEffect(() => {
    const getNetworkSpeed = () => {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        return connection.effectiveType;
      }
      return 'unknown';
    };

    const networkSpeed = getNetworkSpeed();
    const isFastNetwork = networkSpeed === '4g' || networkSpeed === '3g';
    
    const resourcesToLoad = isFastNetwork ? highQualityResources : lowQualityResources;
    
    resourcesToLoad.forEach(resource => {
      preloadResource(resource.url, resource.as, resource.priority);
    });

    console.log(`📡 Network speed: ${networkSpeed}, loading ${isFastNetwork ? 'high' : 'low'} quality resources`);
  }, [highQualityResources, lowQualityResources, preloadResource]);

  return <>{children}</>;
}

/**
 * Utility functions for creating resource configurations
 */
export const createResourceConfig = {
  // Critical resources that should load immediately
  critical: (url: string, as: string = 'script'): ResourcePriority => ({
    url,
    priority: 'high',
    as: as as any,
  }),

  // Non-critical resources that can load later
  deferred: (url: string, as: string = 'script'): ResourcePriority => ({
    url,
    priority: 'low',
    as: as as any,
  }),

  // Font resources with proper configuration
  font: (url: string, type: string = 'font/woff2'): ResourcePriority => ({
    url,
    priority: 'high',
    as: 'font',
    crossOrigin: 'anonymous',
    type,
  }),

  // Image resources
  image: (url: string, priority: 'high' | 'low' = 'low'): ResourcePriority => ({
    url,
    priority,
    as: 'image',
  }),

  // Stylesheet resources
  style: (url: string, priority: 'high' | 'low' = 'high'): ResourcePriority => ({
    url,
    priority,
    as: 'style',
  }),
};
