'use client';

import { useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';

interface PreloadConfig {
  route: string;
  component: () => Promise<any>;
  priority: 'high' | 'medium' | 'low';
  conditions?: {
    userIdle?: boolean;
    networkSpeed?: 'fast' | 'slow' | 'any';
    timeOnPage?: number; // milliseconds
    scrollDepth?: number; // percentage
  };
}

interface IntelligentPreloaderProps {
  preloadConfigs: PreloadConfig[];
  enabled?: boolean;
}

/**
 * Intelligent preloader that loads components based on user behavior
 * and network conditions
 */
export default function IntelligentPreloader({ 
  preloadConfigs, 
  enabled = true 
}: IntelligentPreloaderProps) {
  const router = useRouter();
  const preloadedComponents = useRef(new Set<string>());
  const userIdleTimer = useRef<NodeJS.Timeout>();
  const pageStartTime = useRef(Date.now());
  const scrollDepth = useRef(0);

  // Network speed detection
  const getNetworkSpeed = useCallback((): 'fast' | 'slow' | 'unknown' => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection.effectiveType === '4g' || connection.effectiveType === '3g') {
        return 'fast';
      } else if (connection.effectiveType === '2g' || connection.effectiveType === 'slow-2g') {
        return 'slow';
      }
    }
    return 'unknown';
  }, []);

  // Scroll depth tracking
  const updateScrollDepth = useCallback(() => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
    const currentScrollDepth = (scrollTop / scrollHeight) * 100;
    scrollDepth.current = Math.max(scrollDepth.current, currentScrollDepth);
  }, []);

  // Preload a component
  const preloadComponent = useCallback(async (config: PreloadConfig) => {
    if (preloadedComponents.current.has(config.route)) {
      return; // Already preloaded
    }

    try {
      console.log(`🚀 Preloading component for route: ${config.route}`);
      await config.component();
      preloadedComponents.current.add(config.route);
      
      // Also preload the route itself
      router.prefetch(config.route);
    } catch (error) {
      console.warn(`Failed to preload component for ${config.route}:`, error);
    }
  }, [router]);

  // Check if conditions are met for preloading
  const shouldPreload = useCallback((config: PreloadConfig): boolean => {
    if (!config.conditions) return true;

    const { userIdle, networkSpeed, timeOnPage, scrollDepth: requiredScrollDepth } = config.conditions;
    const currentTime = Date.now();
    const timeSpent = currentTime - pageStartTime.current;
    const networkSpeedActual = getNetworkSpeed();

    // Check network speed condition
    if (networkSpeed && networkSpeed !== 'any') {
      if (networkSpeed === 'fast' && networkSpeedActual === 'slow') return false;
      if (networkSpeed === 'slow' && networkSpeedActual === 'fast') return false;
    }

    // Check time on page condition
    if (timeOnPage && timeSpent < timeOnPage) return false;

    // Check scroll depth condition
    if (requiredScrollDepth && scrollDepth.current < requiredScrollDepth) return false;

    return true;
  }, [getNetworkSpeed]);

  // User idle detection
  const handleUserActivity = useCallback(() => {
    if (userIdleTimer.current) {
      clearTimeout(userIdleTimer.current);
    }

    userIdleTimer.current = setTimeout(() => {
      // User is idle, preload components that require idle state
      preloadConfigs
        .filter(config => config.conditions?.userIdle && shouldPreload(config))
        .sort((a, b) => {
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        })
        .forEach(config => preloadComponent(config));
    }, 2000); // 2 seconds of inactivity
  }, [preloadConfigs, shouldPreload, preloadComponent]);

  // Immediate preloading for high priority components
  const preloadHighPriority = useCallback(() => {
    preloadConfigs
      .filter(config => config.priority === 'high' && shouldPreload(config))
      .forEach(config => preloadComponent(config));
  }, [preloadConfigs, shouldPreload, preloadComponent]);

  // Preload on scroll milestones
  const handleScroll = useCallback(() => {
    updateScrollDepth();
    
    // Preload components based on scroll depth
    preloadConfigs
      .filter(config => 
        config.conditions?.scrollDepth && 
        scrollDepth.current >= config.conditions.scrollDepth &&
        shouldPreload(config)
      )
      .forEach(config => preloadComponent(config));
  }, [preloadConfigs, shouldPreload, preloadComponent, updateScrollDepth]);

  useEffect(() => {
    if (!enabled) return;

    // Immediate high priority preloading
    preloadHighPriority();

    // Set up event listeners
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true });
    });

    // Scroll tracking
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Preload medium priority after initial load
    const mediumPriorityTimer = setTimeout(() => {
      preloadConfigs
        .filter(config => config.priority === 'medium' && shouldPreload(config))
        .forEach(config => preloadComponent(config));
    }, 3000);

    // Preload low priority after longer delay
    const lowPriorityTimer = setTimeout(() => {
      preloadConfigs
        .filter(config => config.priority === 'low' && shouldPreload(config))
        .forEach(config => preloadComponent(config));
    }, 10000);

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserActivity);
      });
      window.removeEventListener('scroll', handleScroll);
      
      if (userIdleTimer.current) {
        clearTimeout(userIdleTimer.current);
      }
      clearTimeout(mediumPriorityTimer);
      clearTimeout(lowPriorityTimer);
    };
  }, [enabled, preloadConfigs, handleUserActivity, handleScroll, preloadHighPriority]);

  return null; // This component doesn't render anything
}

/**
 * Hook for intelligent preloading
 */
export function useIntelligentPreloader(preloadConfigs: PreloadConfig[]) {
  const router = useRouter();
  const preloadedComponents = useRef(new Set<string>());

  const preloadComponent = useCallback(async (config: PreloadConfig) => {
    if (preloadedComponents.current.has(config.route)) return;

    try {
      await config.component();
      preloadedComponents.current.add(config.route);
      router.prefetch(config.route);
    } catch (error) {
      console.warn(`Failed to preload component for ${config.route}:`, error);
    }
  }, [router]);

  const preloadNow = useCallback((route: string) => {
    const config = preloadConfigs.find(c => c.route === route);
    if (config) {
      preloadComponent(config);
    }
  }, [preloadConfigs, preloadComponent]);

  return { preloadNow, preloadedRoutes: Array.from(preloadedComponents.current) };
}

/**
 * Predefined preload configurations for common patterns
 */
export const createPreloadConfig = {
  // Preload on hover (high priority)
  onHover: (route: string, component: () => Promise<any>): PreloadConfig => ({
    route,
    component,
    priority: 'high',
  }),

  // Preload when user is idle (medium priority)
  onIdle: (route: string, component: () => Promise<any>): PreloadConfig => ({
    route,
    component,
    priority: 'medium',
    conditions: { userIdle: true },
  }),

  // Preload after user scrolls (low priority)
  onScroll: (route: string, component: () => Promise<any>, scrollDepth = 25): PreloadConfig => ({
    route,
    component,
    priority: 'low',
    conditions: { scrollDepth },
  }),

  // Preload after time on page (medium priority)
  afterTime: (route: string, component: () => Promise<any>, timeMs = 5000): PreloadConfig => ({
    route,
    component,
    priority: 'medium',
    conditions: { timeOnPage: timeMs },
  }),

  // Preload only on fast networks (low priority)
  onFastNetwork: (route: string, component: () => Promise<any>): PreloadConfig => ({
    route,
    component,
    priority: 'low',
    conditions: { networkSpeed: 'fast' },
  }),
};
